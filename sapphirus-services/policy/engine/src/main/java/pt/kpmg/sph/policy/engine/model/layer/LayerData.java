package pt.kpmg.sph.policy.engine.model.layer;

import jakarta.validation.ValidationException;
import java.io.IOException;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.LinkedHashMap;
import org.apache.commons.lang3.StringUtils;
import pt.kpmg.sph.entities.model.policy.artifactschemas.ArtifactSchemaKey;
import pt.kpmg.sph.entities.model.policy.artifactschemas.ArtifactSchemaKeyMember;
import pt.kpmg.sph.entities.model.policy.artifactschemas.ArtifactSchemaSection;
import pt.kpmg.sph.entities.model.policy.artifactschemas.ArtifactSchemaSectionAttribute;
import pt.kpmg.sph.entities.model.policy.enumgroups.OverrideEnumGroup;
import pt.kpmg.sph.entities.model.policy.ontology.Ontology;
import pt.kpmg.sph.entities.model.policy.ontology.OntologyVersion;
import pt.kpmg.sph.entities.model.policy.ontology.OntologyVersionArtifactAttributeMappingRule;
import pt.kpmg.sph.entities.model.policy.ontology.OntologyVersionArtifactConfiguration;
import pt.kpmg.sph.entities.model.policy.ontology.OntologyVersionArtifactEntityMappingRule;
import pt.kpmg.sph.entities.model.policy.ontology.OntologyVersionArtifactSchema;
import pt.kpmg.sph.entities.model.policy.ontology.OntologyVersionArtifactTransformationRule;
import pt.kpmg.sph.entities.model.policy.ontology.OntologyVersionArtifactValidationRule;
import pt.kpmg.sph.entities.model.policy.ontology.OntologyVersionAttribute;
import pt.kpmg.sph.entities.model.policy.ontology.OntologyVersionBusinessKey;
import pt.kpmg.sph.entities.model.policy.ontology.OntologyVersionEntityType;
import pt.kpmg.sph.entities.model.policy.ontology.OntologyVersionIDVRule;
import pt.kpmg.sph.entities.model.policy.ontology.OntologyVersionIDVRuleCondition;
import pt.kpmg.sph.entities.model.policy.ontology.OntologyVersionIDVRuleResult;
import pt.kpmg.sph.entities.model.policy.ontology.OntologyVersionPartyType;
import pt.kpmg.sph.entities.model.policy.ontology.OntologyVersionRelationshipType;
import pt.kpmg.sph.entities.model.policy.ontology.OntologyVersionRelationshipTypeConfiguration;
import pt.kpmg.sph.policy.engine.mapper.ontologies.ArtifactAttributesFileMapper;
import pt.kpmg.sph.policy.engine.mapper.ontologies.ArtifactConfigurationFileMapper;
import pt.kpmg.sph.policy.engine.mapper.ontologies.ArtifactSectionsFileMapper;
import pt.kpmg.sph.policy.engine.mapper.ontologies.ArtifactTransformationFileMapper;
import pt.kpmg.sph.policy.engine.mapper.ontologies.ArtifactValidationFileMapper;
import pt.kpmg.sph.policy.engine.mapper.ontologies.AttributesFileMapper;
import pt.kpmg.sph.policy.engine.mapper.ontologies.BusinessKeysFileMapper;
import pt.kpmg.sph.policy.engine.mapper.ontologies.CustomRulesFileMapper;
import pt.kpmg.sph.policy.engine.mapper.ontologies.EntityTypeMapper;
import pt.kpmg.sph.policy.engine.mapper.ontologies.FunctionalGroupsFileMapper;
import pt.kpmg.sph.policy.engine.mapper.ontologies.LocalArtifactSchemasFileMapper;
import pt.kpmg.sph.policy.engine.mapper.ontologies.LocalAttributesFileMapper;
import pt.kpmg.sph.policy.engine.mapper.ontologies.LocalEnumFileMapper;
import pt.kpmg.sph.policy.engine.mapper.ontologies.LocalEnumGroupsFileMapper;
import pt.kpmg.sph.policy.engine.mapper.ontologies.LocalEnumValuesFileMapper;
import pt.kpmg.sph.policy.engine.mapper.ontologies.LocalKeysFileMapper;
import pt.kpmg.sph.policy.engine.mapper.ontologies.LocalSectionFileMapper;
import pt.kpmg.sph.policy.engine.mapper.ontologies.OntologyMapper;
import pt.kpmg.sph.policy.engine.mapper.ontologies.PartiesFileMapper;
import pt.kpmg.sph.policy.engine.mapper.ontologies.RelationshipTypesFileMapper;
import pt.kpmg.sph.policy.engine.mapper.ontologies.ResponsibilityConfigurationFileMapper;
import pt.kpmg.sph.policy.engine.mapper.ontologies.RulesFileMapper;
import pt.kpmg.sph.policy.engine.model.layer.enums.EntityType;
import pt.kpmg.sph.policy.engine.model.layer.enums.RuleType;
import pt.kpmg.sph.policy.engine.utils.ArtifactAttributeMappingsUtils;
import pt.kpmg.sph.policy.engine.utils.ArtifactAttributeValidationRulesUtils;
import pt.kpmg.sph.policy.engine.utils.ArtifactSectionMappingsUtils;
import pt.kpmg.sph.policy.engine.utils.ArtifactTransformationRulesUtils;

@Data
@AllArgsConstructor
public class LayerData {

  private OntologyData ontologyVersion;
  private List<FunctionalGroupData> functionalGroups;
  private List<PartyData> parties;
  private List<RelationshipData> relationships;
  private List<AttributeData> attributes;
  private List<AttributeData> attributeData;
  private List<BusinessKeyData> businessKeys;
  private List<IDVRuleData> idRules;
  private List<IDVRuleData> vRules;
  private List<CustomRuleData> customRules;
  private List<ResponsibilityConfigurationData> responsibilityConfiguration;
  private List<LocalArtifactSchemaData> localArtifactSchemas;
  private List<LocalArtifactSectionData> localArtifactSections;
  private List<LocalArtifactAttributeData> localArtifactAttributes;
  private List<LocalArtifactKeyData> localArtifactKeys;
  private List<LocalArtifactEnumGroupData> localArtifactEnumGroups;
  private List<LocalArtifactEnumData> localArtifactEnums;
  private List<LocalArtifactEnumValueData> localArtifactEnumValues;
  private List<ArtifactConfigurationData> artifactConfigurations;
  private List<ArtifactValidationData> artifactValidations;
  private List<ArtifactTransformationData> artifactTransformations;
  private List<ArtifactSectionMappingData> artifactSectionMappings;
  private List<ArtifactAttributeMappingData> artifactAttributeMappings;

  public LayerData(
      OntologyVersion ontologyVersion, String ontologyCode, String ontologyName) {
    if (ontologyVersion == null || StringUtils.isBlank(ontologyCode)) {
      return;
    }
    addOntologyVersion(ontologyVersion, ontologyCode, ontologyName);
    addFunctionalGroups(ontologyVersion);
    addParties(ontologyVersion);
    addRelationshipTypes(ontologyVersion);
    addRules(ontologyVersion.getIdentificationRules(), this.idRules);
    addRules(ontologyVersion.getVerificationRules(), this.vRules);
    addCustomRules(ontologyVersion);
    addResponsibilityConfiguration(ontologyVersion);
    addArtifactSchemas(ontologyVersion);
    addArtifactConfiguration(ontologyVersion);
  }

    private void addOntologyVersion(
      OntologyVersion ontologyVersion, String ontologyCode, String ontologyName) {
    // initialize ontologyVersion
    List<OntologyVersion> versions = new ArrayList<>();
    versions.add(ontologyVersion);
    Ontology ontology = new Ontology();
    ontology.setVersions(versions);
    ontology.setCode(ontologyCode);
    ontology.setName(ontologyName);

    this.ontologyVersion.add(
        OntologyMapper.INSTANCE.mapToOntologyData(ontology, ontologyVersion));
  }

  private void addFunctionalGroups(OntologyVersion ontologyVersion) {
    Optional.ofNullable(ontologyVersion.getFunctionalGroups())
        .ifPresent(
            functionalGroupsList ->
                functionalGroupsList.forEach(
                    functionalGroup ->
                        this.functionalGroups.add(
                            FunctionalGroupsFileMapper.INSTANCE.mapToFunctionalGroupData(
                                functionalGroup))));
  }

  private void addParties(OntologyVersion ontologyVersion) {
    Optional.ofNullable(ontologyVersion.getPartyTypes())
        .ifPresent(
            partyTypes ->
                partyTypes.forEach(
                    partyType -> {
                      this.parties.add(PartiesFileMapper.INSTANCE.mapToPartyData(partyType));
                      addBusinessKeys(partyType);
                      addAttributes(partyType, EntityType.PARTY, partyType.getCode());
                    }));
  }

  private void addAttributes(
      OntologyVersionEntityType partyType, EntityType entityType, String partyCode) {
    Optional.ofNullable(partyType.getAttributes())
        .ifPresent(
            attributesList ->
                attributesList.forEach(
                    attribute ->
                        this.attributes.add(
                            AttributesFileMapper.INSTANCE.mapToAttributeData(
                                attribute, entityType, partyCode))));
  }

  private void addBusinessKeys(OntologyVersionPartyType partyType) {
    Optional.ofNullable(partyType.getBusinessKeys())
        .ifPresent(
            businessKeysList ->
                businessKeysList.forEach(
                    businessKey ->
                        this.businessKeys.add(
                            BusinessKeysFileMapper.INSTANCE.mapToBusinessKeyData(
                                businessKey, partyType.getCode()))));
  }

  private void addRelationshipTypes(OntologyVersion ontologyVersion) {
    Optional.ofNullable(ontologyVersion.getRelationshipTypes())
        .ifPresent(
            relationshipTypes ->
                relationshipTypes.forEach(
                    relationshipType -> {
                      Optional.ofNullable(relationshipType.getConfigurations())
                          .ifPresent(
                              configurations ->
                                  configurations.stream()
                                      .filter(Objects::nonNull)
                                      .forEach(
                                          configuration ->
                                              this.relationships.add(
                                                  RelationshipTypesFileMapper.INSTANCE
                                                      .mapToRelationshipData(
                                                          relationshipType, configuration))));
                      addAttributes(
                          relationshipType, EntityType.RELATIONSHIP, relationshipType.getCode());
                    }));
  }

  private void addRules(
      List<OntologyVersionIDVRule> ontologyVersion, List<IDVRuleData> idRules) {
    Optional.ofNullable(ontologyVersion)
        .ifPresent(
            rules ->
                rules.forEach(
                    rule ->
                        Optional.ofNullable(rule)
                            .ifPresent(
                                nonNullRule -> {
                                  idRules.add(
                                      RulesFileMapper.INSTANCE.mapToIDVRuleData(
                                          nonNullRule,
                                          nonNullRule.getDefaultValue(),
                                          RuleType.DEFAULT,
                                          ""));
                                  Optional.ofNullable(nonNullRule.getConditions())
                                      .ifPresent(
                                          conditions ->
                                              conditions.forEach(
                                                  condition ->
                                                      Optional.ofNullable(condition)
                                                          .ifPresent(
                                                              nonNullCondition ->
                                                                  idRules.add(
                                                                      RulesFileMapper.INSTANCE
                                                                          .mapToIDVRuleData(
                                                                              nonNullRule,
                                                                              nonNullCondition
                                                                                  .getThen(),
                                                                              RuleType.OVERRIDE,
                                                                              nonNullCondition
                                                                                  .getCondition())))));
                                })));
  }

  private void addCustomRules(OntologyVersion ontologyVersion) {
    Optional.ofNullable(ontologyVersion.getCustomRules())
        .ifPresent(
            cRules ->
                cRules.forEach(
                    customRule ->
                        Optional.ofNullable(customRule)
                            .ifPresent(
                                nonNullCustomRule ->
                                    this.customRules.add(
                                        CustomRulesFileMapper.INSTANCE.mapToCustomRuleData(
                                            nonNullCustomRule)))));
  }

  private void addResponsibilityConfiguration(OntologyVersion ontologyVersion) {
    Optional.ofNullable(ontologyVersion.getResponsibilityConfigurations())
        .ifPresent(
            configurations ->
                configurations.forEach(
                    maybeNullConfiguration ->
                        Optional.ofNullable(maybeNullConfiguration)
                            .ifPresent(
                                nonNullConfiguration ->
                                    this.responsibilityConfiguration.add(
                                        ResponsibilityConfigurationFileMapper.INSTANCE
                                            .mapToResponsibilityConfigurationData(
                                                nonNullConfiguration)))));
  }

  private void addArtifactSchemas(OntologyVersion ontologyVersion) {
    Optional.ofNullable(ontologyVersion.getArtifactSchemas())
        .ifPresent(
            artifactSchemas ->
                artifactSchemas.forEach(
                    artifactSchema ->
                        Optional.ofNullable(artifactSchema)
                            .ifPresent(
                                nonNullArtifactSchema -> {
                                  this.localArtifactSchemas.add(
                                      LocalArtifactSchemasFileMapper.INSTANCE
                                          .mapToLocalArtifactSchemaData(
                                              nonNullArtifactSchema));

                                  addArtifactSections(nonNullArtifactSchema);
                                  addArtifactKeys(nonNullArtifactSchema);
                                  addArtifactEnumGroups(nonNullArtifactSchema);
                                })));
  }

  private void addArtifactSections(OntologyVersionArtifactSchema artifactSchema) {
    Optional.ofNullable(artifactSchema.getSections())
        .ifPresent(
            sections ->
                sections.forEach(
                    schemaSection ->
                        Optional.ofNullable(schemaSection)
                            .ifPresent(
                                nonNullSection -> {
                                  this.localArtifactSections.add(
                                      LocalSectionFileMapper.INSTANCE
                                          .mapToLocalArtifactSectionData(
                                              nonNullSection,
                                              artifactSchema.getArtifactSchemaCode()));
                                  addArtifactAttributes(artifactSchema, nonNullSection);
                                })));
  }

  private void addArtifactAttributes(
      OntologyVersionArtifactSchema artifactSchema, ArtifactSchemaSection schemaSection) {
    Optional.ofNullable(schemaSection.getAttributes())
        .ifPresent(
            artifactAttributes ->
                artifactAttributes.forEach(
                    sectionAttribute ->
                        Optional.ofNullable(sectionAttribute)
                            .ifPresent(
                                nonNullAttribute ->
                                    this.localArtifactAttributes.add(
                                        LocalAttributesFileMapper.INSTANCE
                                            .mapToLocalArtifactAttributeData(
                                                nonNullAttribute,
                                                artifactSchema.getArtifactSchemaCode(),
                                                schemaSection.getCode())))));
  }


  private void addArtifactKeys(OntologyVersionArtifactSchema artifactSchema) {
    Optional.ofNullable(artifactSchema.getKeys())
        .ifPresent(
            keys ->
                keys.forEach(
                    artifactSchemaKey ->
                        Optional.ofNullable(artifactSchemaKey.getMembers())
                            .ifPresent(
                                members ->
                                    members.forEach(
                                        member ->
                                            Optional.ofNullable(member)
                                                .ifPresent(
                                                    nonNullMember ->
                                                        this.localArtifactKeys.add(
                                                            LocalKeysFileMapper.INSTANCE
                                                                .mapToLocalArtifactKeyData(
                                                                    artifactSchemaKey,
                                                                    artifactSchema
                                                                        .getArtifactSchemaCode(),
                                                                    nonNullMember.getSection(),
                                                                    nonNullMember
                                                                        .getAttribute())))))));
  }


  private void addArtifactEnumGroups(OntologyVersionArtifactSchema artifactSchema) {
    Optional.ofNullable(artifactSchema.getEnums())
        .ifPresent(
            enumGroups ->
                enumGroups.forEach(
                    enumGroup ->
                        Optional.ofNullable(enumGroup)
                            .ifPresent(
                                nonNullEnumGroup -> {
                                  this.localArtifactEnumGroups.add(
                                      LocalEnumGroupsFileMapper.INSTANCE
                                          .mapToLocalArtifactEnumGroupData(
                                              nonNullEnumGroup,
                                              artifactSchema.getArtifactSchemaCode()));

                                  addArtifactEnumGroupKeys(artifactSchema, nonNullEnumGroup);
                                  addArtifactEnumGroupValues(artifactSchema, nonNullEnumGroup);
                                })));
  }

  private void addArtifactEnumGroupValues(
      OntologyVersionArtifactSchema artifactSchema, OverrideEnumGroup enumGroup) {
    Optional.ofNullable(enumGroup.getValues())
        .ifPresent(
            values ->
                values.forEach(
                    enumGroupValue ->
                        Optional.ofNullable(enumGroupValue)
                            .ifPresent(
                                nonNullValue ->
                                    this.localArtifactEnumValues.add(
                                        LocalEnumValuesFileMapper.INSTANCE
                                            .mapToLocalArtifactEnumValueData(
                                                nonNullValue,
                                                artifactSchema.getArtifactSchemaCode(),
                                                enumGroup.getCode())))));
  }

  private void addArtifactEnumGroupKeys(
      OntologyVersionArtifactSchema artifactSchema, OverrideEnumGroup enumGroup) {
    Optional.ofNullable(enumGroup.getKeys())
        .ifPresent(
            keys ->
                keys.forEach(
                    enumGroupKey ->
                        Optional.ofNullable(enumGroupKey)
                            .ifPresent(
                                nonNullKey ->
                                    this.localArtifactEnums.add(
                                        LocalEnumFileMapper.INSTANCE
                                            .mapToLocalArtifactEnumData(
                                                nonNullKey,
                                                artifactSchema.getArtifactSchemaCode(),
                                                enumGroup.getCode())))));
  }

  private void addArtifactConfiguration(OntologyVersion ontologyVersion) {
    Optional.ofNullable(ontologyVersion.getArtifactConfigurations())
        .ifPresent(
            configurations ->
                configurations.forEach(
                    config ->
                        Optional.ofNullable(config)
                            .ifPresent(
                                nonNullConfig -> {
                                  this.artifactConfigurations.add(
                                      ArtifactConfigurationFileMapper.INSTANCE
                                          .mapToArtifactConfigurationData(nonNullConfig));
                                  addArtifactValidationRules(nonNullConfig);
                                  addArtifactTransformationRules(nonNullConfig);
                                  addArtifactEntityMappingRules(nonNullConfig);
                                  addArtifactAttributeMappingRules(nonNullConfig);
                                })));
  }

  private void addArtifactValidationRules(OntologyVersionArtifactConfiguration config) {
    Optional.ofNullable(config.getAttributeValidationRules())
        .ifPresent(
            validationRules ->
                validationRules.forEach(
                    vRule -> processNestedValidations(vRule, config.getArtifactSchemaCode())));
  }

  private void processNestedValidations(
      OntologyVersionArtifactValidationRule currentRule, String artifactSchemaCode) {
    ArtifactValidationData validationData =
        ArtifactValidationFileMapper.INSTANCE.mapToArtifactValidationData(
            currentRule, artifactSchemaCode);

    this.artifactValidations.add(validationData);

    Optional.ofNullable(currentRule.getSections())
        .ifPresent(
            sections ->
                sections.forEach(section -> processNestedValidations(section, artifactSchemaCode)));
  }

  private void addArtifactTransformationRules(OntologyVersionArtifactConfiguration config) {
    Optional.ofNullable(config.getAttributeTransformationRules())
        .ifPresent(
            transformationRules ->
                transformationRules.forEach(
                    tRule -> processNestedTransformations(tRule, config.getArtifactSchemaCode())));
  }

  private void processNestedTransformations(
      OntologyVersionArtifactTransformationRule currentRule, String artifactSchemaCode) {
    ArtifactTransformationData transformationData =
        ArtifactTransformationFileMapper.INSTANCE.mapToArtifactTransformationData(
            currentRule, artifactSchemaCode);

    this.artifactTransformations.add(transformationData);

    Optional.ofNullable(currentRule.getSections())
        .ifPresent(
            sections ->
                sections.forEach(
                    section -> processNestedTransformations(section, artifactSchemaCode)));
  }

  private void addArtifactEntityMappingRules(OntologyVersionArtifactConfiguration config) {
    Optional.ofNullable(config.getEntityMappingRules())
        .ifPresent(
            entityMappingRules ->
                entityMappingRules.forEach(
                    eRule ->
                        Optional.ofNullable(eRule)
                            .ifPresent(
                                nonNullERule ->
                                    processNestedSections(
                                        nonNullERule,
                                        config.getArtifactSchemaCode(),
                                        new StringBuilder()))));
  }

  private void processNestedSections(
      OntologyVersionArtifactEntityMappingRule currentRule,
      String artifactSchemaCode,
      StringBuilder parentPath) {
    String currentCode = currentRule.getSectionCode();
    String sectionPath = parentPath.length() > 0 ? parentPath + "." + currentCode : currentCode;
    currentRule.setSectionCode(sectionPath);
    ArtifactSectionMappingData mappingData =
        ArtifactSectionsFileMapper.INSTANCE.mapToArtifactSectionMappingData(
            currentRule, artifactSchemaCode);

    artifactSectionMappings.add(mappingData);

    Optional.ofNullable(currentRule.getSections())
        .ifPresent(
            sections ->
                sections.forEach(
                    section ->
                        processNestedSections(
                            section, artifactSchemaCode, new StringBuilder(sectionPath))));
  }

  private void addArtifactAttributeMappingRules(OntologyVersionArtifactConfiguration config) {
    Optional.ofNullable(config.getAttributeMappingRules())
        .ifPresent(
            attributeMappingRules ->
                attributeMappingRules.forEach(
                    aRule -> processNestedAttributes(aRule, config.getArtifactSchemaCode())));
  }

  private void processNestedAttributes(
      OntologyVersionArtifactAttributeMappingRule currentRule, String artifactSchemaCode) {
    ArtifactAttributeMappingData attributeMappingData =
        ArtifactAttributesFileMapper.INSTANCE.mapToArtifactAttributeMappingData(
            currentRule, artifactSchemaCode);

    this.artifactAttributeMappings.add(attributeMappingData);

    Optional.ofNullable(currentRule.getSections())
        .ifPresent(
            sections ->
                sections.forEach(section -> processNestedAttributes(section, artifactSchemaCode)));
  }

  public OntologyVersion toConfig() {
    return processOntology();
  }

  private OntologyVersion processOntology() {
    OntologyVersion ontology = new OntologyVersion();
    if (this.ontologyVersion != null) {
      ontology = OntologyMapper.INSTANCE.mapToOntologyVersion(ontologyVersion);

      processFunctionalGroups(ontology);
      processParties(ontology);
      processRelationshipTypes(ontology);
      processIdentificationRules(ontology);
      processVerificationRules(ontology);
      processResposibilityConfiguration(ontology);
      processCustomRules(ontology);
      processLocalArtifactSchemas(ontology);
      processArtifactConfigurations(ontology);
    }
    return ontology;
  }

  public static void createRules(IDVRuleData idvRuleData, IDVRuleConsumer consumer) {
    OntologyVersionIDVRule rule =
        RulesFileMapper.INSTANCE.mapToOntologyVersionIDVRule(idvRuleData);
    boolean isDefault = idvRuleData.getRuleType() == RuleType.DEFAULT;
    String condition = isDefault ? null : idvRuleData.getConditionExpression();
    OntologyVersionIDVRuleResult ruleResult =
        RulesFileMapper.INSTANCE.mapToOntologyVersionIDVRuleResult(idvRuleData);
    consumer.apply(
        EntityTypeMapper.toLayer(rule.getEntityType()),
        rule.getEntityCode(),
        rule.getAttributeCode(),
        isDefault,
        condition,
        ruleResult);
  }

  private void processFunctionalGroups(OntologyVersion ontology) {
    ontology.setFunctionalGroups(
        this.functionalGroups.stream()
            .map(FunctionalGroupsFileMapper.INSTANCE::mapToOntologyVersionFunctionalGroup)
            .collect(Collectors.toList()));
  }

  private void processParties(OntologyVersion ontology) {
    List<OntologyVersionPartyType> partyTypes =
        this.parties.stream()
            .map(
                party -> {
                  OntologyVersionPartyType partyType =
                      PartiesFileMapper.INSTANCE.mapToOntologyVersionPartyType(party);
                  processBusinessKeys(party, partyType);
                  processPartyTypeAttributes(party, partyType);
                  return partyType;
                })
            .collect(Collectors.toList());

    ontology.setPartyTypes(partyTypes);
  }

  private void processBusinessKeys(PartyData party, OntologyVersionPartyType partyType) {
    List<BusinessKeyData> filteredBusinessKeys =
        this.businessKeys.stream()
            .filter(
                busKey ->
                    busKey != null
                        && StringUtils.equals(busKey.getPartyCode(), party.getPartyCode()))
            .collect(Collectors.toList());

    List<OntologyVersionBusinessKey> ontologyBusinessKeys =
        filteredBusinessKeys.stream()
            .map(BusinessKeysFileMapper.INSTANCE::mapToOntologyVersionBusinessKey)
            .collect(Collectors.toList());

    partyType.setBusinessKeys(ontologyBusinessKeys);
  }

  private void processPartyTypeAttributes(
      PartyData party, OntologyVersionPartyType partyType) {
    List<AttributeData> filteredAttributes =
        this.attributes.stream()
            .filter(
                attributeObj ->
                    attributeObj != null
                        && attributeObj.getEntityType() == EntityType.PARTY
                        && StringUtils.equals(attributeObj.getEntityCode(), party.getPartyCode()))
            .collect(Collectors.toList());

    List<OntologyVersionAttribute> ontologyAttributes =
        filteredAttributes.stream()
            .map(AttributesFileMapper.INSTANCE::mapToOntologyVersionAttribute)
            .collect(Collectors.toList());

    partyType.setAttributes(ontologyAttributes);
  }

  private void processRelationshipTypes(OntologyVersion ontology) {
    ontology.setRelationshipTypes(new ArrayList<>());
    if (!this.relationships.isEmpty()) {
      Map<String, List<RelationshipData>> relationshipsGroupedByCode =
          this.relationships.stream()
              .collect(
                  Collectors.groupingBy(
                      RelationshipData::getRelationshipCode,
                      LinkedHashMap::new, // Use LinkedHashMap to maintain order
                      Collectors.toList()));

      ontology
          .getRelationshipTypes()
          .addAll(
              relationshipsGroupedByCode.values().stream()
                  .map(this::getOntologyVersionRelationshipType)
                  .collect(Collectors.toList()));
    }
  }

  private OntologyVersionRelationshipType getOntologyVersionRelationshipType(
      List<RelationshipData> relationshipList) {
    OntologyVersionRelationshipType relationshipType =
        relationshipList.isEmpty()
            ? new OntologyVersionRelationshipType()
            : RelationshipTypesFileMapper.INSTANCE.mapToOntologyVersionRelationshipType(
                relationshipList.get(0));

    if (!relationshipList.isEmpty()) {
      relationshipType.setConfigurations(
          relationshipList.stream()
              .map(
                  relationship ->
                      new OntologyVersionRelationshipTypeConfiguration(
                          relationship.getParentPartyType(), relationship.getChildPartyType()))
              .collect(Collectors.toList()));

      processRelationshipTypeAttributes(relationshipList, relationshipType);
    }

    return relationshipType;
  }

  private void processRelationshipTypeAttributes(
      List<RelationshipData> relationshipList,
      OntologyVersionRelationshipType relationshipType) {
    if (!this.attributes.isEmpty()) {
      List<AttributeData> filteredAttributes =
          this.attributes.stream()
              .filter(
                  attributeObj ->
                      attributeObj != null
                          && attributeObj.getEntityType() == EntityType.RELATIONSHIP
                          && StringUtils.equals(
                          attributeObj.getEntityCode(),
                          relationshipList.get(0).getRelationshipCode()))
              .collect(Collectors.toList());

      List<OntologyVersionAttribute> ontologyAttributes =
          filteredAttributes.stream()
              .map(AttributesFileMapper.INSTANCE::mapToOntologyVersionAttribute)
              .collect(Collectors.toList());

      relationshipType.setAttributes(ontologyAttributes);
    }
  }

  private void processIdentificationRules(OntologyVersion ontology) {
    ontology.setIdentificationRules(new ArrayList<>());
    for (IDVRuleData idvRuleData : this.idRules) {
      createRules(
          idvRuleData,
          (entityType, entityCode, attributeCode, isDefault, condition, ruleResult) -> {
            OntologyVersionIDVRule rule =
                ontology.getIdentificationRules().stream()
                    .filter(
                        item ->
                            EntityTypeMapper.toLayer(item.getEntityType()) == entityType
                                && item.getEntityCode().equals(entityCode)
                                && item.getAttributeCode().equals(attributeCode))
                    .findFirst()
                    .orElseGet(
                        () -> {
                          OntologyVersionIDVRule result = new OntologyVersionIDVRule();
                          result.setEntityType(EntityTypeMapper.toOntology(entityType));
                          result.setEntityCode(entityCode);
                          result.setAttributeCode(attributeCode);
                          result.setConditions(new ArrayList<>());
                          ontology.getIdentificationRules().add(result);
                          return result;
                        });
            if (isDefault) {
              rule.setDefaultValue(ruleResult);
            } else {
              OntologyVersionIDVRuleCondition ruleCondition = new OntologyVersionIDVRuleCondition();
              ruleCondition.setCondition(condition);
              ruleCondition.setThen(ruleResult);
              rule.getConditions().add(ruleCondition);
            }
          });
    }
  }


  private void processVerificationRules(OntologyVersion ontology) {
    ontology.setVerificationRules(new ArrayList<>());
    for (IDVRuleData idvRuleData : this.vRules) {
      createRules(
          idvRuleData,
          (entityType, entityCode, attributeCode, isDefault, condition, ruleResult) -> {
            OntologyVersionIDVRule rule =
                ontology.getVerificationRules().stream()
                    .filter(
                        item ->
                            EntityTypeMapper.toLayer(item.getEntityType()) == entityType
                                && item.getEntityCode().equals(entityCode)
                                && item.getAttributeCode().equals(attributeCode))
                    .findFirst()
                    .orElseGet(
                        () -> {
                          OntologyVersionIDVRule result = new OntologyVersionIDVRule();
                          result.setEntityType(EntityTypeMapper.toOntology(entityType));
                          result.setEntityCode(entityCode);
                          result.setAttributeCode(attributeCode);
                          result.setConditions(new ArrayList<>());
                          ontology.getVerificationRules().add(result);
                          return result;
                        });
            if (isDefault) {
              rule.setDefaultValue(ruleResult);
            } else {
              OntologyVersionIDVRuleCondition ruleCondition = new OntologyVersionIDVRuleCondition();
              ruleCondition.setCondition(condition);
              ruleCondition.setThen(ruleResult);
              rule.getConditions().add(ruleCondition);
            }
          });
    }
  }

  private void processResposibilityConfiguration(OntologyVersion ontology) {
    ontology.setResponsibilityConfigurations(
        this.responsibilityConfiguration.stream()
            .map(
                ResponsibilityConfigurationFileMapper.INSTANCE
                    ::mapToOntologyVersionResponsibilityConfiguration)
            .collect(Collectors.toList()));
  }

  private void processCustomRules(OntologyVersion ontology) {
    ontology.setCustomRules(
        this.customRules.stream()
            .map(CustomRulesFileMapper.INSTANCE::mapToOntologyVersionPolicyRule)
            .collect(Collectors.toList()));
  }

  private void processLocalArtifactSchemas(OntologyVersion ontology) {
    List<OntologyVersionArtifactSchema> artifactSchemas =
        this.localArtifactSchemas.stream()
            .map(
                artifactSchemaSheetData -> {
                  OntologyVersionArtifactSchema artifactSchema =
                      LocalArtifactSchemasFileMapper.INSTANCE.mapToOntologyVersionArtifactSchema(
                          artifactSchemaSheetData);
                  processLocalArtifactSections(artifactSchema);
                  return artifactSchema;
                })
            .collect(Collectors.toList());

    ontology.setArtifactSchemas(artifactSchemas);
  }

  private void processLocalArtifactSections(OntologyVersionArtifactSchema artifactSchema) {
    if (!this.localArtifactSections.isEmpty()) {
      artifactSchema.setSections(new ArrayList<>());
      artifactSchema.setKeys(new ArrayList<>());
      List<LocalArtifactSectionData> filteredSections =
          this.localArtifactSections.stream()
              .filter(
                  sectionObj ->
                      sectionObj != null
                          && Objects.equals(
                          artifactSchema.getArtifactSchemaCode(),
                          sectionObj.getArtifactSchemaCode()))
              .collect(Collectors.toList());
      filteredSections.forEach(
          schemaSection -> {
            ArtifactSchemaSection section =
                LocalSectionFileMapper.INSTANCE.mapToArtifactSchemaSection(schemaSection);

            processLocalArtifactAttributes(artifactSchema, schemaSection, section);
            artifactSchema.getSections().add(section);
          });
      processArtifactEnumGroups(artifactSchema);
    }
  }

  private void processLocalArtifactAttributes(
      OntologyVersionArtifactSchema artifactSchema,
      LocalArtifactSectionData schemaSection,
      ArtifactSchemaSection section) {

    List<LocalArtifactAttributeData> filteredAttributes =
        localArtifactAttributes.stream()
            .filter(
                attributeObj ->
                    attributeObj != null
                        && Objects.equals(
                        artifactSchema.getArtifactSchemaCode(),
                        attributeObj.getArtifactSchemaCode())
                        && Objects.equals(
                        schemaSection.getSectionCode(), attributeObj.getSectionCode()))
            .collect(Collectors.toList());

    List<ArtifactSchemaSectionAttribute> sectionAttributes =
        filteredAttributes.stream()
            .map(
                sectionAttribute -> {
                  ArtifactSchemaSectionAttribute attribute =
                      LocalAttributesFileMapper.INSTANCE.mapToArtifactSchemaSectionAttribute(
                          sectionAttribute);
                  processLocalArtifactKeys(artifactSchema, schemaSection, sectionAttribute);
                  return attribute;
                })
            .collect(Collectors.toList());

    section.setAttributes(sectionAttributes);
  }

  private void processArtifactEnumGroups(OntologyVersionArtifactSchema artifactSchema) {
    List<LocalArtifactEnumGroupData> filteredEnumGroups =
        this.localArtifactEnumGroups.stream()
            .filter(
                enumGroupObj ->
                    enumGroupObj != null
                        && Objects.equals(
                        artifactSchema.getArtifactSchemaCode(),
                        enumGroupObj.getArtifactSchemaCode()))
            .collect(Collectors.toList());

    List<OverrideEnumGroup> enumGroups =
        filteredEnumGroups.stream()
            .map(
                enumGroupData -> {
                  OverrideEnumGroup enumGroup =
                      LocalEnumGroupsFileMapper.INSTANCE.mapToEnumGroup(enumGroupData);
                  processArtifactEnumKeys(artifactSchema, enumGroupData, enumGroup);
                  processArtifactEnumValues(artifactSchema, enumGroupData, enumGroup);
                  return enumGroup;
                })
            .collect(Collectors.toList());

    artifactSchema.setEnums(enumGroups);
  }

  private void processArtifactEnumKeys(
      OntologyVersionArtifactSchema artifactSchema,
      LocalArtifactEnumGroupData enumGroupData,
      OverrideEnumGroup enumGroup) {
    List<LocalArtifactEnumData> filteredEnums =
        localArtifactEnums.stream()
            .filter(
                enumObj ->
                    enumObj != null
                        && Objects.equals(
                        artifactSchema.getArtifactSchemaCode(), enumObj.getArtifactSchemaCode())
                        && Objects.equals(
                        enumGroupData.getEnumGroupCode(), enumObj.getEnumGroupCode()))
            .collect(Collectors.toList());

    enumGroup.setKeys(
        filteredEnums.stream()
            .map(LocalEnumFileMapper.INSTANCE::mapToEnumGroupKey)
            .collect(Collectors.toList()));
  }

  private void processArtifactEnumValues(
      OntologyVersionArtifactSchema artifactSchema,
      LocalArtifactEnumGroupData enumGroupData,
      OverrideEnumGroup enumGroup) {
    List<LocalArtifactEnumValueData> filteredEnumValues =
        localArtifactEnumValues.stream()
            .filter(
                enumValObj ->
                    enumValObj != null
                        && Objects.equals(
                        artifactSchema.getArtifactSchemaCode(),
                        enumValObj.getArtifactSchemaCode())
                        && Objects.equals(
                        enumGroupData.getEnumGroupCode(), enumValObj.getEnumGroupCode()))
            .collect(Collectors.toList());

    enumGroup.setValues(
        filteredEnumValues.stream()
            .map(LocalEnumValuesFileMapper.INSTANCE::mapToEnumGroupValue)
            .collect(Collectors.toList()));
  }

  private void processLocalArtifactKeys(
      OntologyVersionArtifactSchema artifactSchema,
      LocalArtifactSectionData schemaSection,
      LocalArtifactAttributeData sectionAttribute) {
    List<LocalArtifactKeyData> filteredKeys =
        this.localArtifactKeys.stream()
            .filter(
                keyObj ->
                    keyObj != null
                        && Objects.equals(
                        artifactSchema.getArtifactSchemaCode(), keyObj.getArtifactSchemaCode())
                        && Objects.equals(schemaSection.getSectionCode(), keyObj.getSectionCode())
                        && Objects.equals(
                        sectionAttribute.getAttributeCode(), keyObj.getAttributeCode()))
            .collect(Collectors.toList());
    List<ArtifactSchemaKey> keys =
        filteredKeys.stream()
            .map(
                artifactSchemaKey -> {
                  ArtifactSchemaKey key =
                      LocalKeysFileMapper.INSTANCE.mapToArtifactSchemaKey(artifactSchemaKey);
                  List<ArtifactSchemaKeyMember> members = new ArrayList<>();
                  members.add(
                      new ArtifactSchemaKeyMember(
                          artifactSchemaKey.getSectionCode(),
                          artifactSchemaKey.getAttributeCode()));
                  key.setMembers(members);
                  return key;
                })
            .collect(Collectors.toList());

    artifactSchema.getKeys().addAll(keys);
  }

  private void processArtifactConfigurations(OntologyVersion ontology) {
    ontology.setArtifactConfigurations(
        artifactConfigurations.stream()
            .map(
                configSheetData -> {
                  OntologyVersionArtifactConfiguration config =
                      ArtifactConfigurationFileMapper.INSTANCE
                          .mapToOntologyVersionArtifactConfiguration(configSheetData);
                  try {
                    ArtifactAttributeValidationRulesUtils.processArtifactAttributeValidationRules(
                        config, artifactValidations);
                    ArtifactTransformationRulesUtils.processArtifactTransformationRules(
                        config, artifactTransformations);
                    ArtifactSectionMappingsUtils.processArtifactSectionMappings(
                        config, artifactSectionMappings);
                    ArtifactAttributeMappingsUtils.processArtifactAttributeMappings(
                        config, artifactAttributeMappings);
                  } catch (IOException e) {
                    throw new ValidationException(e);
                  }
                  return config;
                })
            .collect(Collectors.toList()));
  }

  @FunctionalInterface
  public interface IDVRuleConsumer {

    void apply(
        EntityType entityType,
        String entityCode,
        String attributeCode,
        boolean isDefault,
        String condition,
        OntologyVersionIDVRuleResult rule);
  }
}
