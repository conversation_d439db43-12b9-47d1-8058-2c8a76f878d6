package pt.kpmg.sph.policy.engine.mapper.ontologies.layerdata;

import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;
import java.util.List;
import java.util.ArrayList;
import pt.kpmg.sph.policy.engine.model.layer.LayerData;
import pt.kpmg.sph.policy.engine.model.layer.OntologyData;
import pt.kpmg.sph.policy.excel.data.ontologies.OntologyFileData;
import pt.kpmg.sph.policy.excel.data.ontologies.OntologySheetData;

@Mapper(
    uses = {
      OntologyDataMapper.class,
      FunctionalGroupDataMapper.class,
      PartyDataMapper.class,
      RelationshipDataMapper.class,
      ArtifactSectionMappingDataMapper.class,
      BusinessKeyDataMapper.class,
      IDVRuleDataMapper.class,
      CustomRuleDataMapper.class,
      ResponsibilityConfigurationDataMapper.class,
      LocalArtifactSchemaDataMapper.class,
      LocalArtifactSectionDataMapper.class,
      LocalArtifactAttributeDataMapper.class,
      LocalArtifactKeyDataMapper.class,
      LocalArtifactEnumGroupDataMapper.class,
      LocalArtifactEnumDataMapper.class,
      LocalArtifactEnumValueDataMapper.class,
      ArtifactConfigurationDataMapper.class,
      ArtifactValidationDataMapper.class,
      ArtifactTransformationDataMapper.class,
      ArtifactAttributeMappingDataMapper.class
    })
public interface LayerDataMapper {

  LayerDataMapper INSTANCE = Mappers.getMapper(LayerDataMapper.class);

  @Mapping(target = "ontologyVersion", expression = "java(mapOntologyVersion(layerData.getOntologyVersion()))")
  @Mapping(source = "functionalGroups", target = "functionalGroups")
  @Mapping(source = "parties", target = "parties")
  @Mapping(source = "relationships", target = "relationships")
  @Mapping(source = "attributes", target = "attributes")
  @Mapping(source = "businessKeys", target = "businessKeys")
  @Mapping(source = "idRules", target = "idRules")
  @Mapping(source = "VRules", target = "VRules")
  @Mapping(source = "customRules", target = "customRules")
  @Mapping(source = "responsibilityConfiguration", target = "responsibilityConfiguration")
  @Mapping(source = "localArtifactSchemas", target = "localArtifactSchemas")
  @Mapping(source = "localArtifactSections", target = "localArtifactSections")
  @Mapping(source = "localArtifactAttributes", target = "localArtifactAttributes")
  @Mapping(source = "localArtifactKeys", target = "localArtifactKeys")
  @Mapping(source = "localArtifactEnumGroups", target = "localArtifactEnumGroups")
  @Mapping(source = "localArtifactEnums", target = "localArtifactEnums")
  @Mapping(source = "localArtifactEnumValues", target = "localArtifactEnumValues")
  @Mapping(source = "artifactConfigurations", target = "artifactConfigurations")
  @Mapping(source = "artifactValidations", target = "artifactValidations")
  @Mapping(source = "artifactTransformations", target = "artifactTransformations")
  @Mapping(source = "artifactSectionMappings", target = "artifactSectionMappings")
  @Mapping(source = "artifactAttributeMappings", target = "artifactAttributeMappings")
  OntologyFileData mapToOntologyFileData(LayerData layerData);

  @Mapping(target = "ontologyVersion", expression = "java(mapOntologyVersionFromSheet(ontologyFileData.getOntologyVersion()))")
  @Mapping(source = "functionalGroups", target = "functionalGroups")
  @Mapping(source = "parties", target = "parties")
  @Mapping(source = "relationships", target = "relationships")
  @Mapping(source = "attributes", target = "attributes")
  @Mapping(source = "businessKeys", target = "businessKeys")
  @Mapping(source = "idRules", target = "idRules")
  @Mapping(source = "VRules", target = "VRules")
  @Mapping(source = "customRules", target = "customRules")
  @Mapping(source = "responsibilityConfiguration", target = "responsibilityConfiguration")
  @Mapping(source = "localArtifactSchemas", target = "localArtifactSchemas")
  @Mapping(source = "localArtifactSections", target = "localArtifactSections")
  @Mapping(source = "localArtifactAttributes", target = "localArtifactAttributes")
  @Mapping(source = "localArtifactKeys", target = "localArtifactKeys")
  @Mapping(source = "localArtifactEnumGroups", target = "localArtifactEnumGroups")
  @Mapping(source = "localArtifactEnums", target = "localArtifactEnums")
  @Mapping(source = "localArtifactEnumValues", target = "localArtifactEnumValues")
  @Mapping(source = "artifactConfigurations", target = "artifactConfigurations")
  @Mapping(source = "artifactValidations", target = "artifactValidations")
  @Mapping(source = "artifactTransformations", target = "artifactTransformations")
  @Mapping(source = "artifactSectionMappings", target = "artifactSectionMappings")
  @Mapping(source = "artifactAttributeMappings", target = "artifactAttributeMappings")
  LayerData mapToLayerData(OntologyFileData ontologyFileData);

  default List<OntologySheetData> mapOntologyVersion(OntologyData data) {
    if (data == null) return new ArrayList<>();
    return List.of(OntologyDataMapper.INSTANCE.toOntologySheetData(data));
  }

  default OntologyData mapOntologyVersionFromSheet(List<OntologySheetData> sheetData) {
    if (sheetData == null || sheetData.isEmpty()) return null;
    return OntologyDataMapper.INSTANCE.toOntologyData(sheetData.get(0));
  }
}
