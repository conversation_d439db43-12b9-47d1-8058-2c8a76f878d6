{"ontologyVersion": {"ontologyCode": "TEST_ONTOLOGY", "ontologyName": "Test Ontology", "ontologyVersion": 1, "lastUpdatedOn": "2024-01-15T10:30:00Z", "hash": "abc123hash", "extensionCode": "EXT001", "extensionsName": "Test Extension", "extensionsVersion": "1.0", "allowAddParties": true, "allowAddRelationships": true, "allowCustomRules": true, "allowAddResponsabilities": true, "allowAddFunctionalGroups": true, "allowAddLocalArtifact": true, "allowAddArtifactUsage": true}, "functionalGroups": [{"functionalGroupCode": "KYC", "functionalGroupName": "Know Your Customer", "functionalGroupDescription": "KYC processes and validations", "layerAction": "NEW", "allowEdit": true, "allowDisable": false}, {"functionalGroupCode": "AML", "functionalGroupName": "Anti Money Laundering", "functionalGroupDescription": "AML processes and validations", "layerAction": "NEW", "allowEdit": true, "allowDisable": false}], "parties": [{"partyCode": "INDIVIDUAL", "partyName": "Individual", "partyDescription": "Individual person", "allowMultipleArtifactSources": true, "artifactPrecedence": ["ID_DOCUMENT", "PASSPORT"], "layerAction": "NEW", "allowEdit": true, "allowDisable": false, "allowAddAttributes": true, "allowAddBusinessKeys": true}, {"partyCode": "COMPANY", "partyName": "Company", "partyDescription": "Company entity", "allowMultipleArtifactSources": false, "artifactPrecedence": ["INCORPORATION_DOCUMENT"], "layerAction": "NEW", "allowEdit": true, "allowDisable": false, "allowAddAttributes": true, "allowAddBusinessKeys": true}], "relationships": [{"relationshipCode": "OWNS", "relationshipName": "Ownership", "relationshipDescription": "Ownership relationship", "parentPartyType": "INDIVIDUAL", "childPartyType": "COMPANY", "allowMultipleArtifactSources": true, "artifactPrecedence": ["OWNERSHIP_DOCUMENT"], "layerAction": "NEW", "allowEdit": true, "allowDisable": false, "allowAddAttributes": true}, {"relationshipCode": "CONTROLS", "relationshipName": "Control", "relationshipDescription": "Control relationship", "parentPartyType": "INDIVIDUAL", "childPartyType": "COMPANY", "allowMultipleArtifactSources": false, "artifactPrecedence": ["CONTROL_DOCUMENT"], "layerAction": "NEW", "allowEdit": true, "allowDisable": false, "allowAddAttributes": true}], "attributes": [{"entityCode": "INDIVIDUAL", "attributeCode": "FIRST_NAME", "attributeName": "First Name", "attributeDescription": "Person's first name", "entityType": "PARTY", "attributeType": "STRING", "comparisonStrategy": "DEFAULT", "comparisonClassName": "StringComparator", "layerAction": "NEW", "allowEdit": true, "allowDisable": false, "allowEditIDRules": true, "allowEditVRules": true}, {"entityCode": "INDIVIDUAL", "attributeCode": "LAST_NAME", "attributeName": "Last Name", "attributeDescription": "Person's last name", "entityType": "PARTY", "attributeType": "STRING", "comparisonStrategy": "DEFAULT", "comparisonClassName": "StringComparator", "layerAction": "NEW", "allowEdit": true, "allowDisable": false, "allowEditIDRules": true, "allowEditVRules": true}, {"entityCode": "OWNS", "attributeCode": "OWNERSHIP_PERCENTAGE", "attributeName": "Ownership Percentage", "attributeDescription": "Percentage of ownership", "entityType": "RELATIONSHIP", "attributeType": "NUMBER", "comparisonStrategy": "DEFAULT", "comparisonClassName": "DecimalComparator", "layerAction": "NEW", "allowEdit": true, "allowDisable": false, "allowEditIDRules": true, "allowEditVRules": true}], "attributeData": [{"entityCode": "COMPANY", "attributeCode": "COMPANY_NAME", "attributeName": "Company Name", "attributeDescription": "Legal name of the company", "entityType": "PARTY", "attributeType": "STRING", "comparisonStrategy": "DEFAULT", "comparisonClassName": "StringComparator", "layerAction": "NEW", "allowEdit": true, "allowDisable": false, "allowEditIDRules": true, "allowEditVRules": true}], "businessKeys": [{"partyCode": "INDIVIDUAL", "attributeCodes": ["FIRST_NAME", "LAST_NAME"], "order": 1, "otherAttributesToValidate": ["BIRTH_DATE"], "keyName": "Primary Key", "keyDescription": "Primary identification key for individuals", "layerAction": "NEW", "allowEdit": true, "allowDisable": false, "keyStrength": "STRONG"}, {"partyCode": "COMPANY", "attributeCodes": ["COMPANY_NAME", "TAX_ID"], "order": 1, "otherAttributesToValidate": ["INCORPORATION_DATE"], "keyName": "Company Primary Key", "keyDescription": "Primary identification key for companies", "layerAction": "NEW", "allowEdit": true, "allowDisable": false, "keyStrength": "STRONG"}], "idRules": [{"entityCode": "INDIVIDUAL", "attributeCode": "FIRST_NAME", "conditionExpression": "length(FIRST_NAME) > 0", "ruleCode": "FIRST_NAME_REQUIRED", "ruleName": "First Name Required", "ruleDescription": "First name is required for individuals", "isRequired": true, "reasonCode": "MISSING_FIRST_NAME", "reasonDescription": "First name is missing", "artifactPrecedence": ["ID_DOCUMENT", "PASSPORT"], "layerAction": "NEW", "entityType": "PARTY", "ruleType": "DEFAULT"}, {"entityCode": "INDIVIDUAL", "attributeCode": "LAST_NAME", "conditionExpression": "length(LAST_NAME) > 0", "ruleCode": "LAST_NAME_REQUIRED", "ruleName": "Last Name Required", "ruleDescription": "Last name is required for individuals", "isRequired": true, "reasonCode": "MISSING_LAST_NAME", "reasonDescription": "Last name is missing", "artifactPrecedence": ["ID_DOCUMENT", "PASSPORT"], "layerAction": "NEW", "entityType": "PARTY", "ruleType": "OVERRIDE"}], "vRules": [{"entityCode": "INDIVIDUAL", "attributeCode": "FIRST_NAME", "conditionExpression": "isValidName(FIRST_NAME)", "ruleCode": "FIRST_NAME_VALIDATION", "ruleName": "First Name Validation", "ruleDescription": "Validates first name format", "isRequired": false, "reasonCode": "INVALID_FIRST_NAME", "reasonDescription": "First name format is invalid", "artifactPrecedence": ["EXTERNAL_VALIDATION"], "layerAction": "NEW", "entityType": "PARTY", "ruleType": "OVERRIDE"}], "customRules": [{"entityCode": "INDIVIDUAL", "attributeCodePattern": "*", "ruleCode": "CUSTOM_RULE_1", "ruleName": "Custom Rule 1", "ruleDescription": "Custom validation rule for individuals", "applyWhen": "always", "ruleExpression": "true", "reasonCode": "CUSTOM_FAIL", "reasonDescription": "Custom rule failed", "layerAction": "NEW", "allowEdit": true, "allowDisable": false, "entityType": "PARTY", "ruleLevel": "ATTRIBUTE", "ruleResult": "PASS"}], "responsibilityConfiguration": [{"targetEntityExpression": "INDIVIDUAL", "responsibleEntityExpression": "ADMIN", "attributeNamePattern": "*", "ruleCode": "RESP_RULE_1", "delegationCode": "DELEGATION_1", "delegationName": "Delegation 1", "delegationDescription": "Responsibility delegation for individuals", "layerAction": "NEW", "allowEdit": true, "allowDisable": false, "level": "PARTY"}], "localArtifactSchemas": [{"artifactSchemaCode": "LOCAL_SCHEMA_1", "artifactSchemaName": "Local Schema 1", "artifactSchemaDescription": "Local artifact schema for testing", "isIndexable": true, "tags": ["tag1", "tag2"], "layerAction": "NEW", "allowEdit": true, "allowDisable": false, "allowAddSections": true}], "localArtifactSections": [{"artifactSchemaCode": "LOCAL_SCHEMA_1", "sectionCode": "SECTION_1", "sectionName": "Section 1", "sectionDescription": "Local artifact section for testing", "isRepeatable": true, "isMandatory": false, "layerAction": "NEW", "allowEdit": true, "allowDisable": false, "allowAddAttributes": true}], "localArtifactAttributes": [{"artifactSchemaCode": "LOCAL_SCHEMA_1", "sectionCode": "SECTION_1", "attributeCode": "ATTR_1", "attributeName": "Attribute 1", "attributeDescription": "Local artifact attribute for testing", "pattern": ".*", "layerAction": "NEW", "allowEdit": true, "allowDisable": false, "allowAddKeys": true, "allowAddEnums": true, "attributeType": "STRING"}], "localArtifactKeys": [{"artifactSchemaCode": "LOCAL_SCHEMA_1", "sectionCode": "SECTION_1", "attributeCode": "ATTR_1", "keyGroup": "GROUP_1", "keyName": "Key 1", "keyDescription": "Local artifact key for testing", "layerAction": "NEW", "allowEdit": true, "allowDisable": false}], "localArtifactEnumGroups": [{"artifactSchemaCode": "LOCAL_SCHEMA_1", "enumGroupCode": "ENUM_GROUP_1", "enumGroupName": "Enum Group 1", "enumGroupDescription": "Local artifact enum group for testing", "overrideGlobalEnumGroup": false, "layerAction": "NEW", "allowEdit": true, "allowDisable": false, "allowAddEnums": true}], "localArtifactEnums": [{"artifactSchemaCode": "LOCAL_SCHEMA_1", "enumGroupCode": "ENUM_GROUP_1", "enumCode": "ENUM_1", "enumName": "Enum 1", "enumDescription": "Local artifact enum for testing", "layerAction": "NEW", "allowEdit": true, "allowDisable": false, "allowAddEnumValues": true, "enumColumn": "COL1"}], "localArtifactEnumValues": [{"artifactSchemaCode": "LOCAL_SCHEMA_1", "enumGroupCode": "ENUM_GROUP_1", "col1": "Value1", "col2": "Value2", "col3": "Value3", "col4": "Value4", "col5": "Value5", "col6": "Value6", "col7": "Value7", "col8": "Value8", "col9": "Value9", "col10": "Value10", "otherValues": ["Other1", "Other2"], "layerAction": "NEW", "allowEdit": true, "allowDisable": false}], "artifactConfigurations": [{"artifactSchemaCode": "ARTIFACT_SCHEMA_1", "artifactSchemaVersion": 1, "functionalGroupCodes": ["KYC", "AML"], "isRecyclable": true, "considerForMismatchValues": true, "considerForMissingValues": false, "expirationDateExpression": "now() + 365 days", "layerAction": "NEW", "allowEdit": true, "allowDisable": false, "allowAddSectionMappings": true, "usageType": "GLOBAL", "recycleType": "AUTOMATIC"}], "artifactValidations": [{"artifactSchemaCode": "ARTIFACT_SCHEMA_1", "sectionCodePattern": "SECTION_*", "attributeCodePattern": "ATTR_*", "validationName": "Validation 1", "validationDescription": "Artifact validation rule for testing", "parameters": ["param1", "param2"], "validateWhen": "always", "reasonCode": "VALIDATION_FAIL", "reasonDescription": "Validation failed", "layerAction": "NEW", "allowEdit": true, "allowDisable": false, "validationLevel": "ATTRIBUTE", "validationMethod": "REGEX", "applyValidation": "BOTH"}], "artifactTransformations": [{"artifactSchemaCode": "ARTIFACT_SCHEMA_1", "sectionCodePattern": "SECTION_*", "attributeCodePattern": "ATTR_*", "attributeValuePattern": ".*", "transformedValueExpression": "toUpperCase(value)", "transformWhen": "always", "transformationName": "Transformation 1", "transformationDescription": "Artifact transformation rule for testing", "layerAction": "NEW", "allowEdit": true, "allowDisable": false}], "artifactSectionMappings": [{"artifactSchemaCode": "ARTIFACT_SCHEMA_1", "sectionCodePattern": "SECTION_*", "entityIdentifier": "INDIVIDUAL", "mappingName": "Section Mapping 1", "mappingDescription": "Artifact section mapping for testing", "entityType": "PARTY", "entityCodeExpression": "INDIVIDUAL", "parentIdentifier": "ROOT", "childIdentifier": "CHILD", "mapWhen": "always", "isIndexableForRecycling": true, "layerAction": "NEW", "allowEdit": true, "allowDisable": false, "allowAddAttributeMappings": true}], "artifactAttributeMappings": [{"artifactSchemaCode": "ARTIFACT_SCHEMA_1", "sectionCode": "SECTION_1", "attributeCodeExpression": "ATTR_*", "entityIdentifier": "INDIVIDUAL", "mappingName": "Attribute Mapping 1", "mappingDescription": "Artifact attribute mapping for testing", "entityCodePattern": "INDIVIDUAL", "destinationAttributeCode": "FIRST_NAME", "mapWhen": "always", "layerAction": "NEW", "allowEdit": true, "allowDisable": false, "allowAddAttributeValidations": true, "allowAddAttributeTransformations": true, "entityType": "PARTY"}]}